import ElementReveal from "@/components/animations/element-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { services } from "@/data";
import { ArrowRight } from "lucide-react";
import { motion } from "motion/react";
import Image from "next/image";
import Link from "next/link";

export default function ServiceSection() {
   return (
      <>
         <section className="py-20 bg-background">
            <div className="container max-w-[1400px] mx-auto px-6">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Our{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Services
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground font-montserrat max-w-2xl mx-auto">
                        From weddings to special events, we offer comprehensive
                        photography and videography services tailored to your
                        unique vision.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {services.map((service, index) => (
                     <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        viewport={{ once: true }}
                     >
                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group relative overflow-hidden">
                           <div className="absolute inset-0">
                              <Image
                                 src={service.image}
                                 alt={service.title}
                                 fill
                                 className="object-cover transition-transform duration-300 group-hover:scale-105 opacity-80"
                                 sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              />
                              <div className="absolute inset-0 bg-gradient-overlay"></div>
                           </div>
                           <CardContent className="p-6 text-center z-10">
                              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4 group-hover:animate-glow transition-all">
                                 <service.icon className="h-8 w-8 text-white" />
                              </div>
                              <h3 className="text-xl font-semibold text-white mb-2">
                                 {service.title}
                              </h3>
                              <p className="text-gray-200 text-sm">
                                 {service.description}
                              </p>
                           </CardContent>
                        </Card>
                     </motion.div>
                  ))}
               </div>

               <ElementReveal delay={0.2} className="text-center mt-12">
                  <Button
                     asChild
                     size="lg"
                     className="bg-gradient-accent h-auto py-3 !px-6"
                  >
                     <Link href="/services">
                        View All Services{" "}
                        <ArrowRight className="ml-2 h-5 w-5" />
                     </Link>
                  </Button>
               </ElementReveal>
            </div>
         </section>
      </>
   );
}
