"use client";

interface Props {
   src: string;
   width: number;
   quality: number;
}

export default function cloudflareLoader({ src, width, quality }: Props) {
   if (process.env.NODE_ENV === "development") {
      return src;
   }

   const params = [`width=${width}`, `quality=${quality || 75}`, "format=auto"];

   if (src.startsWith("/")) {
      return `https://images.astralstudios.co.uk/cdn-cgi/image/${params.join(
         ","
      )}/https://astralstudios.co.uk${src}`;
   }

   return `https://images.astralstudios.co.uk/cdn-cgi/image/${params.join(
      ","
   )}/${src}`;
}
