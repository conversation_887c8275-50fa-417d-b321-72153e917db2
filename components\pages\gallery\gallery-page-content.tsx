"use client";

import TextReveal from "@/components/animations/text-reveal";
import {
   GalleryGrid,
   GallerySearch,
   LoadMoreButton,
} from "@/components/pages/gallery";
import AlbumCard from "@/components/pages/gallery/album/album-card";
import { StructuredData } from "@/components/structured-data";
import { Button } from "@/components/ui/button";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
   useInfinitePublicAlbums,
   useInfiniteSearchAlbums,
} from "@/lib/hooks/use-albums";
import Lenis from "@studio-freight/lenis";
import { motion } from "framer-motion";
import { Grid3X3, SortAsc, SortDesc } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";

const ITEMS_PER_PAGE = 12;

export default function GalleryPageContent() {
   const [searchQuery, setSearchQuery] = useState("");
   const [sortBy, setSortBy] = useState("createdAt");
   const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

   // Track previous album count to identify new albums for animation
   const previousAlbumCountRef = useRef(0);

   // Fetch data based on search or normal listing
   const isSearching = searchQuery.trim().length > 0;

   // Infinite scroll for public albums
   const {
      data: albumsData,
      isLoading: albumsLoading,
      hasNextPage: albumsHasNextPage,
      isFetchingNextPage: albumsIsFetchingNextPage,
      fetchNextPage: albumsFetchNextPage,
   } = useInfinitePublicAlbums({
      limit: ITEMS_PER_PAGE,
      sortBy,
      sortOrder,
   });

   // Infinite scroll for search results
   const {
      data: searchData,
      isLoading: searchLoading,
      hasNextPage: searchHasNextPage,
      isFetchingNextPage: searchIsFetchingNextPage,
      fetchNextPage: searchFetchNextPage,
   } = useInfiniteSearchAlbums(searchQuery, {
      limit: ITEMS_PER_PAGE,
      sortBy,
      sortOrder,
   });

   // Determine which data to use
   const currentData = isSearching ? searchData : albumsData;
   const isLoading = isSearching ? searchLoading : albumsLoading;
   const hasNextPage = isSearching ? searchHasNextPage : albumsHasNextPage;
   const isFetchingNextPage = isSearching
      ? searchIsFetchingNextPage
      : albumsIsFetchingNextPage;
   const fetchNextPage = isSearching
      ? searchFetchNextPage
      : albumsFetchNextPage;

   // Flatten all albums from all pages
   const albums = useMemo(() => {
      if (!currentData?.pages) return [];
      return currentData.pages.flatMap((page) => page.data);
   }, [currentData?.pages]);

   // Get pagination info from first page
   const pagination = currentData?.pages?.[0]?.pagination;

   const handleLoadMore = () => {
      if (hasNextPage && !isFetchingNextPage) {
         fetchNextPage();
      }
   };

   const handleSortChange = (value: string) => {
      const [newSortBy, newSortOrder] = value.split("-");
      setSortBy(newSortBy);
      setSortOrder(newSortOrder as "asc" | "desc");
   };

   // Update previous album count after animations complete
   useEffect(() => {
      if (albums.length > previousAlbumCountRef.current) {
         // Delay updating the ref to allow animations to complete
         const timer = setTimeout(() => {
            previousAlbumCountRef.current = albums.length;
         }, 600); // Slightly longer than animation duration

         return () => clearTimeout(timer);
      }
   }, [albums.length]);

   // Reset previous count when search query or sort changes
   useEffect(() => {
      previousAlbumCountRef.current = 0;
   }, [searchQuery, sortBy, sortOrder]);

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   return (
      <div className="min-h-screen">
         {/* Structured Data */}
         <StructuredData
            type="imageGallery"
            data={{
               name: "Astral Studios Gallery",
               description:
                  "Explore our stunning collection of photography and videography work, organized into beautiful albums that showcase the artistry and emotion of every moment we capture.",
               url: "https://www.astralstudios.co.uk/gallery",
               albums: albums.slice(0, 10).map((album) => ({
                  name: album.name,
                  description: album.description,
                  url: `https://www.astralstudios.co.uk/gallery/albums/${album._id}`,
                  coverImageUrl: album.coverImageUrl,
                  createdAt: album.createdAt,
               })),
            }}
         />

         {/* Hero Section */}
         <section className="pt-36 pb-8 bg-gradient-hero">
            <div className="container mx-auto px-4">
               <div className="text-center max-w-4xl mx-auto">
                  <TextReveal>
                     <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6 leading-18">
                        Our{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Gallery
                        </span>
                     </h1>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                        Explore our stunning collection of photography and
                        videography work, organized into beautiful albums that
                        showcase the artistry and emotion of every moment we
                        capture.
                     </p>
                  </TextReveal>
                  {/* <ElementReveal className="flex flex-col sm:flex-row items-center justify-center gap-4">
                     <Button asChild size="lg">
                        <Link href="/gallery/albums">View All Albums</Link>
                     </Button>
                  </ElementReveal> */}
               </div>
            </div>
         </section>

         <div className="container mx-auto px-4 mb-12">
            <div className="mt-8 mb-12">
               {/* Search */}
               <GallerySearch
                  onSearch={setSearchQuery}
                  placeholder="Search albums by name..."
                  className="mb-8"
               />

               {/* Controls */}
               <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  <div />

                  {/* Sort */}
                  <div className="flex items-center gap-2">
                     <span className="text-sm text-muted-foreground">
                        Sort by:
                     </span>
                     <Select
                        value={`${sortBy}-${sortOrder}`}
                        onValueChange={handleSortChange}
                     >
                        <SelectTrigger className="w-48">
                           <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value="createdAt-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Newest First
                              </div>
                           </SelectItem>
                           <SelectItem value="createdAt-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Oldest First
                              </div>
                           </SelectItem>
                           <SelectItem value="name-asc">
                              <div className="flex items-center gap-2">
                                 <SortAsc className="w-4 h-4" />
                                 Name A-Z
                              </div>
                           </SelectItem>
                           <SelectItem value="name-desc">
                              <div className="flex items-center gap-2">
                                 <SortDesc className="w-4 h-4" />
                                 Name Z-A
                              </div>
                           </SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
               </div>
            </div>

            {/* Results Info */}
            {isSearching && (
               <div className="mb-8">
                  {searchLoading ? (
                     <p className="text-muted-foreground">
                        Searching albums...
                     </p>
                  ) : (
                     <p className="text-muted-foreground">
                        {albums.length > 0 ? (
                           <>
                              Found {pagination?.total || albums.length} albums
                              for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        ) : (
                           <>
                              No albums found for &quot;
                              <span className="text-foreground font-medium">
                                 {searchQuery}
                              </span>
                              &quot;
                           </>
                        )}
                     </p>
                  )}
               </div>
            )}

            {/* Albums Grid */}
            {isLoading ? (
               <GalleryGrid columns={4}>
                  {Array.from({ length: 4 }).map((_, i) => (
                     <div key={i} className="space-y-4">
                        <Skeleton className="aspect-[4/3] rounded-2xl" />
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                     </div>
                  ))}
               </GalleryGrid>
            ) : albums.length > 0 ? (
               <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                     {albums.map((album, index) => {
                        // Only animate new albums (those beyond the previous count)
                        const isNewAlbum =
                           index >= previousAlbumCountRef.current;

                        return (
                           <motion.div
                              key={String(album._id)}
                              initial={
                                 isNewAlbum ? { opacity: 0, y: 20 } : false
                              }
                              animate={isNewAlbum ? { opacity: 1, y: 0 } : {}}
                              transition={{
                                 duration: 0.5,
                                 delay: isNewAlbum
                                    ? (index - previousAlbumCountRef.current) *
                                      0.1
                                    : 0,
                                 ease: "easeOut",
                              }}
                           >
                              <AlbumCard
                                 album={album}
                                 variant="default"
                                 showStats={true}
                                 showDescription={true}
                              />
                           </motion.div>
                        );
                     })}
                  </div>

                  {/* Pagination */}
                  <div className="flex flex-col items-center gap-4">
                     {/* Load More Button */}
                     {hasNextPage && (
                        <LoadMoreButton
                           onClick={handleLoadMore}
                           isLoading={isFetchingNextPage}
                           hasMore={hasNextPage}
                           buttonText="Load More Albums"
                           loadingText="Loading more albums..."
                        />
                     )}

                     {/* Pagination Info */}
                     {pagination && (
                        <div className="text-sm text-muted-foreground text-center">
                           Showing {albums.length} of {pagination.total} albums
                        </div>
                     )}
                  </div>
               </>
            ) : (
               <div className="text-center py-20">
                  <Grid3X3 className="w-20 h-20 mx-auto mb-6 text-muted-foreground/50" />
                  <h3 className="text-2xl font-semibold text-foreground mb-4">
                     {isSearching ? "No Albums Found" : "No Albums Available"}
                  </h3>
                  <p className="text-muted-foreground text-lg mb-8 max-w-md mx-auto">
                     {isSearching
                        ? "Try adjusting your search terms or filters to find what you're looking for."
                        : "We're working on adding new albums. Check back soon!"}
                  </p>
                  {isSearching && (
                     <Button
                        onClick={() => {
                           setSearchQuery("");
                        }}
                        variant="outline"
                     >
                        Clear Search
                     </Button>
                  )}
               </div>
            )}
         </div>
      </div>
   );
}
