"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion, MotionValue, useTransform } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";

type CardProps = {
   i: number;
   title: string;
   description: string;
   image: string;
   progress: MotionValue<number>;
   range: [number, number];
   targetScale: number;
   href: string;
   features?: string[];
};

const Card = ({
   i,
   title,
   description,
   image,
   progress,
   range,
   targetScale,
   href,
   features = [],
}: CardProps) => {
   const container = useRef(null);

   const scale = useTransform(progress, range, [1, targetScale]);

   return (
      <div
         ref={container}
         // className="h-screen flex justify-center sticky top-46"
         className="h-[calc(100%+500px)] flex justify-center sticky top-36 lg:top-58"
      >
         <motion.div
            key={title}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            // className="flex flex-col relative top-[-25%] h-fit overflow-hidden w-full max-w-5xl rounded-[25px] origin-top bg-astral-black group border border-border/70"
            className="flex flex-col relative top-[-25%] h-fit overflow-hidden w-full max-w-5xl rounded-[25px] origin-top bg-astral-black group border border-border/70"
            style={{
               scale,
               top: `calc(-5vh + ${i * 25}px)`,
            }}
         >
            <div
               className={`grid grid-cols-1 lg:grid-cols-2 items-center lg:h-[400px]`}
            >
               {/* Image */}
               <div className={`relative overflow-hidden`}>
                  <div className="relative w-full h-[30vh] lg:h-[400px]">
                     <Image
                        src={image}
                        alt={title}
                        fill
                        className="object-cover object-top transition-transform duration-700 hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                     />
                     <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  </div>
               </div>

               {/* Content */}
               <div className={`space-y-4 sm:space-y-6 p-6 sm:p-10`}>
                  <div>
                     <h3 className="text-xl lg:text-2xl font-playfair font-bold text-foreground mb-4">
                        {title}
                     </h3>
                     <p className="sm:text-lg text-muted-foreground leading-relaxed">
                        {description}
                     </p>
                     {features.length > 0 && (
                        <div className="pt-4">
                           <ul className="space-y-1 text-sm text-muted-foreground mb-4">
                              {features.map((feature, index) => (
                                 <li key={index} className="flex items-center">
                                    <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0" />
                                    {feature}
                                 </li>
                              ))}
                           </ul>
                        </div>
                     )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                     <Button
                        asChild
                        size="lg"
                        className="group/btn py-3 h-auto"
                     >
                        <Link href={href}>
                           Learn More
                           <ArrowRight className="h-5 w-5 ml-2 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                     </Button>
                  </div>
               </div>
            </div>
         </motion.div>
      </div>
   );
};

export default Card;
